import { Badge, type BadgeConfig } from "@components/common";
import { cn } from "@utils/cn";

export type TaskStatusType = "completed" | "duedate" | "overdue" | "upcoming";

interface TaskStatusBadgeConfig extends BadgeConfig {
  type: TaskStatusType;
}

const TASK_STATUS_BADGE_CONFIG: Record<TaskStatusType, TaskStatusBadgeConfig> = {
  completed: {
    containerClasses: "bg-neutral/10 !text-label-xs border border-neutral/20",
    icon: <div className="size-2 rounded-full bg-neutral/50" />,
    iconAlt: "completed",
    textClasses: "text-neutral/70",
    type: "completed",
  },
  duedate: {
    containerClasses: "bg-primary/15 !text-label-xs border border-primary/30 shadow-sm",
    icon: <div className="size-2 rounded-full bg-primary" />,
    iconAlt: "duedate",
    textClasses: "text-primary font-semibold",
    type: "duedate",
  },
  overdue: {
    containerClasses: "bg-error/10 !text-label-xs border border-error/25",
    icon: <div className="size-2 rounded-full bg-error/70" />,
    iconAlt: "overdue",
    textClasses: "text-error/80",
    type: "overdue",
  },
  upcoming: {
    containerClasses: "bg-info/8 !text-label-xs border border-info/20",
    icon: <div className="size-2 rounded-full bg-info/60" />,
    iconAlt: "upcoming",
    textClasses: "text-info/70",
    type: "upcoming",
  },
};

interface TaskStatusBadgeProps {
  type: TaskStatusType;
  className?: string;
  label?: string;
}

export const TaskStatusBadge = ({ type, className, label }: TaskStatusBadgeProps) => {
  const badgeConfig: Record<TaskStatusType, TaskStatusBadgeConfig> = label
    ? {
        ...TASK_STATUS_BADGE_CONFIG,
        [type]: {
          ...TASK_STATUS_BADGE_CONFIG[type],
          label,
        },
      }
    : TASK_STATUS_BADGE_CONFIG;

  return <Badge type={type} className={cn("rounded-lg", className)} config={badgeConfig} />;
};

export const TASK_STATUS_OPTIONS = (
  ["completed", "duedate", "overdue", "upcoming"] as TaskStatusType[]
).map((type) => ({
  label: <TaskStatusBadge type={type} />,
  value: type,
}));
