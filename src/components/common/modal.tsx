// import { XIcon } from "@phosphor-icons/react";
// import { But<PERSON> } from "./button";

/**
 * Open the modal using:
 * onClick={() => (document.getElementById("my_modal") as HTMLDialogElement)?.showModal()}
 * */
export const Modal = ({ children }: { children: React.ReactNode }) => {
  return (
    <dialog id="my_modal" className="modal">
      <div className="modal-box">
        {/* <form method="dialog">
          <Button variant="icon" type="submit" className="absolute top-2 right-2">
            <XIcon size={24} weight="bold" />
          </Button>
        </form> */}
        {children}
      </div>
    </dialog>
  );
};
