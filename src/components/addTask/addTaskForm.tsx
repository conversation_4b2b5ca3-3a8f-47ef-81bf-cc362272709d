import { useForm } from "@tanstack/react-form";
import { cn } from "@utils/cn";
import { useTranslation } from "react-i18next";
import { Button, Input, Textarea } from "../common";

interface AddTaskFormValues {
  assignee: string;
  detail: string;
  duedate: string;
  source: string;
  title: string;
}

export const AddTaskForm = () => {
  const { t } = useTranslation();

  const form = useForm({
    defaultValues: {
      assignee: "",
      detail: "",
      duedate: "",
      source: "",
      title: "",
    },
    onSubmit: async ({ value }: { value: AddTaskFormValues }) => {
      alert(value);
    },
  });
  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <form.Field name="title">
        {(fieldApi) => (
          <Input
            id={fieldApi.name}
            type="text"
            placeholder="Title"
            value={fieldApi.state.value}
            onChange={(e) => fieldApi.handleChange(e.target.value)}
            className={cn("flex-1")}
          />
        )}
      </form.Field>
      <form.Field name="detail">
        {(fieldApi) => (
          <Textarea
            className="w-full resize-none"
            id={fieldApi.name}
            placeholder="Detail"
            value={fieldApi.state.value}
            onChange={(e) => fieldApi.handleChange(e.target.value)}
          />
        )}
      </form.Field>
      <form.Field name="source">
        {(fieldApi) => (
          <Input
            id={fieldApi.name}
            type="text"
            placeholder="Source"
            value={fieldApi.state.value}
            onChange={(e) => fieldApi.handleChange(e.target.value)}
            className={cn("flex-1")}
          />
        )}
      </form.Field>
      <form.Field name="duedate">
        {(fieldApi) => (
          <Input
            id={fieldApi.name}
            type="date"
            value={fieldApi.state.value}
            onChange={(e) => fieldApi.handleChange(e.target.value)}
            className="flex-1"
          />
        )}
      </form.Field>
      <form.Field name="assignee">
        {(fieldApi) => (
          <input
            id={fieldApi.name}
            type="text"
            placeholder="Assignee"
            value={fieldApi.state.value}
            onChange={(e) => fieldApi.handleChange(e.target.value)}
          />
        )}
      </form.Field>
      <div className="flow-root" />
      <div className="flex justify-end gap-3">
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
          {([canSubmit, isSubmitting]) => (
            <Button
              variant="outline"
              className={cn(
                "w-28 border-primary text-primary",
                "hover:border-primary-content hover:bg-primary-content/10 hover:text-primary-content",
                { "border-none text-base-100": !canSubmit },
              )}
              type="button"
              disabled={!canSubmit || isSubmitting}
              onClick={() => (document.getElementById("my_modal") as HTMLDialogElement)?.close()}
            >
              {isSubmitting ? t("common.loading") : t("common.cancel")}
            </Button>
          )}
        </form.Subscribe>

        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
          {([canSubmit, isSubmitting]) => (
            <Button className="w-28" type="submit" disabled={!canSubmit || isSubmitting}>
              {isSubmitting ? t("common.loading") : t("common.save")}
            </Button>
          )}
        </form.Subscribe>
      </div>
    </form>
  );
};
