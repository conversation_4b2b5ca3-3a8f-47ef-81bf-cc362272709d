import { Avatar, Container } from "@components/common";
import { But<PERSON> } from "@components/common/button";
import { CaretLeftIcon } from "@phosphor-icons/react";
import { useRouter } from "@tanstack/react-router";
import { LeadProfileForm } from "./profileForm";
import { ProfileTasks } from "./profileTasks";

const TaskDetail = () => {
  const router = useRouter();
  return (
    <Container>
      <div className="gap-4 border-container">
        <div className="flex items-center justify-between">
          <Button variant="ghost">
            <CaretLeftIcon size={24} weight="bold" onClick={() => router.history.back()} />
          </Button>
          <div className="flex items-center gap-2 ">
            <h5>Assignee</h5>
            <Avatar />
          </div>
        </div>
        <ProfileTasks />
      </div>
    </Container>
  );
};

const LeadDetail = () => {
  return (
    <Container className="w-1/2">
      <div className="border-container border-none">
        <LeadProfileForm />
      </div>
    </Container>
  );
};

export function Profile() {
  return (
    <>
      <TaskDetail />
      <LeadDetail />
    </>
  );
}
