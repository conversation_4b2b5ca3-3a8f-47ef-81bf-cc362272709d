import { AddTaskForm } from "@components/addTask";
import { TaskStatusBadge, type TaskStatusType } from "@components/badge";
import { <PERSON><PERSON>, Button, Container, Modal } from "@components/common";
import { CheckFatIcon, DotsThreeOutlineVerticalIcon, PlusSquareIcon } from "@phosphor-icons/react";
import { cn } from "@utils/cn";
import { getToday, isDateTodayOrFuture } from "@utils/date";
import { useState } from "react";
import type { TaskProps } from "./interface";
import { tasks } from "./mock";

const CARD_STYLE = {
  completed: "text-neutral-content bg-base-200/30 border border-base-300",
  duedate:
    "bg-success-content border-primary/80 border-dashed shadow-lg ring-1 ring-offset-3 ring-primary",
  overdue: "text-secondary bg-secondary-content/20 border-dashed border-secondary-content",
  upcoming: "text-info/80 bg-base-200 border border-base-300",
};

const taskStatus = (
  isCompleted: boolean,
  dueDate: string,
): { label: string; type: TaskStatusType } => {
  if (isCompleted) {
    return { label: "Completed", type: "completed" };
  }
  if (dueDate === getToday() && !isCompleted) {
    return { label: "Today", type: "duedate" };
  }
  if (isDateTodayOrFuture(dueDate)) {
    return { label: "Upcoming", type: "upcoming" };
  }
  return { label: "Overdue", type: "overdue" };
};

const TaskCard = ({ task }: { task: TaskProps }) => {
  const [isCompleted, setIsCompleted] = useState(false);
  const { label, type } = taskStatus(task.isCompleted, task.duedate);
  const isToday = task.duedate === getToday();

  return (
    <Container
      className={cn("!h-fit gap-4 rounded-lg border p-2", CARD_STYLE[type], {
        "text-base-content": isToday,
      })}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h4>{task.title}</h4>
          <TaskStatusBadge type={type} label={label} className="rounded-lg bg-white" />
          <p className="text-label-xs">{task.duedate}</p>
        </div>
        <div className="flex items-center gap-2">
          {task.duedate === getToday() && (
            <Button
              disabled={isCompleted || type === "completed"}
              variant="icon"
              onClick={() => setIsCompleted(!isCompleted)}
              className="size-6 bg-white ring-2"
            >
              {(isCompleted || type === "completed") && (
                <CheckFatIcon
                  size={18}
                  weight="fill"
                  className={cn({ "text-primary": isCompleted })}
                />
              )}
            </Button>
          )}
          <DotsThreeOutlineVerticalIcon size={16} weight="fill" />
          {/* <Modal /> */}
        </div>
      </div>

      <div className="flow-root" />

      <div className="flex items-start gap-2">
        <p className="text-label-sm">Detail:</p>
        <p className="whitespace-pre-wrap text-xs">{task.detail}</p>
      </div>

      <div className="flow-root" />

      <div className="flex items-start gap-2">
        <p className="text-label-sm">Source:</p>
        <a href={task.source} className="text-blue-400 text-body-xs underline">
          {task.source}
        </a>
      </div>

      <div className="flow-root" />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <p className="text-label-xs">Assignee :</p>
          <Avatar image={task.assignee} />
        </div>
        <p className="text-label-xs">Create at : {task.createdAt}</p>
      </div>
    </Container>
  );
};

export const ProfileTasks = () => {
  return (
    <div className="m-4 flex-1 space-y-4 overflow-auto p-2">
      {tasks.map((task) => (
        <div key={task.title} className="flex flex-col items-center gap-2">
          {isDateTodayOrFuture(task.duedate) && !task.isCompleted && (
            <>
              <PlusSquareIcon
                size={24}
                onClick={() =>
                  (document.getElementById("my_modal") as HTMLDialogElement)?.showModal()
                }
                type="button"
                className="cursor-pointer"
              />
              <Modal>
                <AddTaskForm />
              </Modal>
            </>
          )}
          <TaskCard task={task} />
        </div>
      ))}
    </div>
  );
};
