import { getToday } from "@utils/date";
import type { LeadProps } from "./interface";

export const leads: LeadProps[] = [
  {
    contactChannel: "line",
    followUpDate: getToday(),
    followUpStatus: "contacted",
    leadStatus: "draft",
    name: "<PERSON>",
    opportunity: "hot",
    servicesOfInterest: "botox",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    contactChannel: "instagram",
    followUpDate: getToday(),
    followUpStatus: "interested",
    leadStatus: "active",
    name: "<PERSON> Don<PERSON>",
    opportunity: "hot",
    servicesOfInterest: "hifu",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    contactChannel: "tiktok",
    followUpDate: getToday(),
    followUpStatus: "pending",
    leadStatus: "completed",
    name: "<PERSON>",
    opportunity: "warm",
    servicesOfInterest: "thermage",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: true, task: "Follow up" },
    ],
  },
  {
    contactChannel: "facebook",
    followUpDate: getToday(),
    followUpStatus: "not_interested",
    leadStatus: "suspended",
    name: "John Doreamon",
    opportunity: "cold",
    servicesOfInterest: "juvelook",
    tasks: [
      { completed: false, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
];
