const formatDate = (date: Date) =>
  new Intl.DateTimeFormat("th-TH", {
    day: "2-digit",
    month: "2-digit",
    year: "2-digit",
  }).format(date);

export const getToday = () => formatDate(new Date());

export const getDateWithOffset = (offsetDays: number) => {
  const date = new Date();
  date.setDate(date.getDate() + offsetDays);
  return formatDate(date);
};

export const getYesterday = () => getDateWithOffset(-1);

export const isDateTodayOrFuture = (dateString: string): boolean => {
  const todayString = getToday();
  return dateString >= todayString;
};
